/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.21.1+3772"}
 */

/**
 * Import the modules used in this configuration.
 */
const GPIO    = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1   = GPIO.addInstance();
const GPIO2   = GPIO.addInstance();
const GPIO3   = GPIO.addInstance();
const GPIO4   = GPIO.addInstance();
const GPIO5   = GPIO.addInstance();
const GPIO6   = GPIO.addInstance();
const GPIO7   = GPIO.addInstance();
const GPIO8   = GPIO.addInstance();
const I2C     = scripting.addModule("/ti/driverlib/I2C", {}, false);
const I2C1    = I2C.addInstance();
const PWM     = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1    = PWM.addInstance();
const SYSCTL  = scripting.addModule("/ti/driverlib/SYSCTL");
const SYSTICK = scripting.addModule("/ti/driverlib/SYSTICK");
const TIMER   = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1  = TIMER.addInstance();
const UART    = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1   = UART.addInstance();
const UART2   = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
const divider6       = system.clockTree["PLL_CLK2X_DIV"];
divider6.divideValue = 4;

const divider7       = system.clockTree["PLL_PDIV"];
divider7.divideValue = 2;

const divider9       = system.clockTree["UDIV"];
divider9.divideValue = 2;

const multiplier2         = system.clockTree["PLL_QDIV"];
multiplier2.multiplyValue = 10;

const mux8       = system.clockTree["HSCLKMUX"];
mux8.inputSelect = "HSCLKMUX_SYSPLL2X";

GPIO1.$name                         = "GPIO_EncoderA";
GPIO1.port                          = "PORTA";
GPIO1.associatedPins.create(2);
GPIO1.associatedPins[0].$name       = "PIN_1";
GPIO1.associatedPins[0].direction   = "INPUT";
GPIO1.associatedPins[0].interruptEn = true;
GPIO1.associatedPins[0].polarity    = "RISE";
GPIO1.associatedPins[0].pin.$assign = "PA25";
GPIO1.associatedPins[1].$name       = "PIN_0";
GPIO1.associatedPins[1].direction   = "INPUT";
GPIO1.associatedPins[1].interruptEn = true;
GPIO1.associatedPins[1].polarity    = "RISE";
GPIO1.associatedPins[1].pin.$assign = "PA26";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO2.$name                               = "KEY";
GPIO2.associatedPins.create(3);
GPIO2.associatedPins[0].$name             = "KEY1";
GPIO2.associatedPins[0].direction         = "INPUT";
GPIO2.associatedPins[0].internalResistor  = "PULL_UP";
GPIO2.associatedPins[0].interruptEn       = true;
GPIO2.associatedPins[0].polarity          = "FALL";
GPIO2.associatedPins[0].interruptPriority = "3";
GPIO2.associatedPins[0].pin.$assign       = "PA0";
GPIO2.associatedPins[1].$name             = "KEY2";
GPIO2.associatedPins[1].direction         = "INPUT";
GPIO2.associatedPins[1].internalResistor  = "PULL_UP";
GPIO2.associatedPins[1].interruptEn       = true;
GPIO2.associatedPins[1].polarity          = "FALL";
GPIO2.associatedPins[1].interruptPriority = "3";
GPIO2.associatedPins[1].pin.$assign       = "PA13";
GPIO2.associatedPins[2].$name             = "KEY3";
GPIO2.associatedPins[2].direction         = "INPUT";
GPIO2.associatedPins[2].internalResistor  = "PULL_UP";
GPIO2.associatedPins[2].interruptEn       = true;
GPIO2.associatedPins[2].interruptPriority = "3";
GPIO2.associatedPins[2].polarity          = "FALL";
GPIO2.associatedPins[2].pin.$assign       = "PA14";

GPIO3.$name                          = "LED";
GPIO3.associatedPins[0].$name        = "LED1";
GPIO3.associatedPins[0].initialValue = "SET";
GPIO3.associatedPins[0].pin.$assign  = "PA8";

GPIO4.$name                         = "MotorA";
GPIO4.port                          = "PORTB";
GPIO4.associatedPins.create(2);
GPIO4.associatedPins[0].$name       = "AIN1";
GPIO4.associatedPins[0].pin.$assign = "PB19";
GPIO4.associatedPins[1].$name       = "AIN2";
GPIO4.associatedPins[1].pin.$assign = "PB20";

GPIO5.$name                         = "MotorB";
GPIO5.port                          = "PORTA";
GPIO5.associatedPins.create(2);
GPIO5.associatedPins[0].$name       = "BIN1";
GPIO5.associatedPins[0].pin.$assign = "PA23";
GPIO5.associatedPins[1].$name       = "BIN2";
GPIO5.associatedPins[1].pin.$assign = "PA24";

GPIO6.port                          = "PORTA";
GPIO6.$name                         = "GPIO_EncoderB";
GPIO6.associatedPins.create(2);
GPIO6.associatedPins[0].direction   = "INPUT";
GPIO6.associatedPins[0].interruptEn = true;
GPIO6.associatedPins[0].polarity    = "RISE";
GPIO6.associatedPins[0].$name       = "PIN_3";
GPIO6.associatedPins[0].pin.$assign = "PA27";
GPIO6.associatedPins[1].direction   = "INPUT";
GPIO6.associatedPins[1].interruptEn = true;
GPIO6.associatedPins[1].polarity    = "RISE";
GPIO6.associatedPins[1].$name       = "PIN_4";
GPIO6.associatedPins[1].pin.$assign = "PA9";

GPIO7.$name                              = "BUZZER";
GPIO7.port                               = "PORTA";
GPIO7.associatedPins[0].$name            = "BUZZER1";
GPIO7.associatedPins[0].assignedPin      = "7";
GPIO7.associatedPins[0].internalResistor = "PULL_DOWN";
GPIO7.associatedPins[0].pin.$assign      = "PA7";

GPIO8.$name                              = "Digital";
GPIO8.associatedPins.create(7);
GPIO8.associatedPins[0].$name            = "D0";
GPIO8.associatedPins[0].direction        = "INPUT";
GPIO8.associatedPins[0].internalResistor = "PULL_DOWN";
GPIO8.associatedPins[0].assignedPort     = "PORTB";
GPIO8.associatedPins[0].assignedPin      = "8";
GPIO8.associatedPins[0].pin.$assign      = "PB8";
GPIO8.associatedPins[1].$name            = "D1";
GPIO8.associatedPins[1].direction        = "INPUT";
GPIO8.associatedPins[1].internalResistor = "PULL_DOWN";
GPIO8.associatedPins[1].assignedPort     = "PORTA";
GPIO8.associatedPins[1].assignedPin      = "15";
GPIO8.associatedPins[1].pin.$assign      = "PA15";
GPIO8.associatedPins[2].$name            = "D2";
GPIO8.associatedPins[2].direction        = "INPUT";
GPIO8.associatedPins[2].assignedPort     = "PORTA";
GPIO8.associatedPins[2].assignedPin      = "16";
GPIO8.associatedPins[2].internalResistor = "PULL_DOWN";
GPIO8.associatedPins[3].$name            = "D3";
GPIO8.associatedPins[3].direction        = "INPUT";
GPIO8.associatedPins[3].internalResistor = "PULL_DOWN";
GPIO8.associatedPins[3].assignedPort     = "PORTA";
GPIO8.associatedPins[3].assignedPin      = "17";
GPIO8.associatedPins[3].pin.$assign      = "PA17";
GPIO8.associatedPins[4].$name            = "D4";
GPIO8.associatedPins[4].direction        = "INPUT";
GPIO8.associatedPins[4].internalResistor = "PULL_DOWN";
GPIO8.associatedPins[4].assignedPort     = "PORTA";
GPIO8.associatedPins[4].assignedPin      = "18";
GPIO8.associatedPins[4].pin.$assign      = "PA18";
GPIO8.associatedPins[5].$name            = "D5";
GPIO8.associatedPins[5].direction        = "INPUT";
GPIO8.associatedPins[5].internalResistor = "PULL_DOWN";
GPIO8.associatedPins[5].assignedPin      = "18";
GPIO8.associatedPins[5].assignedPort     = "PORTB";
GPIO8.associatedPins[5].pin.$assign      = "PB18";
GPIO8.associatedPins[6].$name            = "D6";
GPIO8.associatedPins[6].direction        = "INPUT";
GPIO8.associatedPins[6].internalResistor = "PULL_DOWN";
GPIO8.associatedPins[6].assignedPort     = "PORTB";
GPIO8.associatedPins[6].assignedPin      = "9";
GPIO8.associatedPins[6].pin.$assign      = "PB9";

I2C1.$name                             = "I2C_OLED";
I2C1.basicEnableController             = true;
I2C1.basicControllerStandardBusSpeed   = "Fast";
I2C1.intController                     = ["ARBITRATION_LOST","NACK","RXFIFO_TRIGGER","RX_DONE","TX_DONE"];
I2C1.peripheral.$assign                = "I2C0";
I2C1.peripheral.sdaPin.$assign         = "PA10";
I2C1.peripheral.sclPin.$assign         = "PA16";
I2C1.sdaPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C1.sdaPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C1.sdaPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
I2C1.sdaPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric11";
I2C1.sclPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C1.sclPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C1.sclPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
I2C1.sclPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric12";

PWM1.$name                              = "Motor_PWM";
PWM1.timerCount                         = 2500;
PWM1.pwmMode                            = "EDGE_ALIGN_UP";
PWM1.peripheral.$assign                 = "TIMG6";
PWM1.peripheral.ccp0Pin.$assign         = "PB2";
PWM1.peripheral.ccp1Pin.$assign         = "PB3";
PWM1.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC1";
PWM1.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric0";
PWM1.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric5";

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;

SYSTICK.interruptEnable   = true;
SYSTICK.interruptPriority = "3";
SYSTICK.systickEnable     = true;
SYSTICK.periodEnable      = true;
SYSTICK.period            = 80000;

TIMER1.$name              = "TIMER_Encoder_Read";
TIMER1.timerClkPrescale   = 256;
TIMER1.timerMode          = "PERIODIC";
TIMER1.interrupts         = ["ZERO"];
TIMER1.timerPeriod        = "5 ms";
TIMER1.peripheral.$assign = "TIMG0";

UART1.$name                    = "UART_0";
UART1.targetBaudRate           = 115200;
UART1.rxFifoThreshold          = "DL_UART_RX_FIFO_LEVEL_ONE_ENTRY";
UART1.enabledInterrupts        = ["RX"];
UART1.interruptPriority        = "3";
UART1.peripheral.$assign       = "UART0";
UART1.peripheral.rxPin.$assign = "PA31";
UART1.peripheral.txPin.$assign = "PA28";
UART1.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric1";
UART1.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric2";

UART2.$name                    = "UART_1";
UART2.targetBaudRate           = 115200;
UART2.enabledInterrupts        = ["RX"];
UART2.interruptPriority        = "1";
UART2.peripheral.rxPin.$assign = "PB7";
UART2.peripheral.txPin.$assign = "PB6";
UART2.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric3";
UART2.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric4";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
Board.peripheral.$suggestSolution            = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution   = "PA20";
Board.peripheral.swdioPin.$suggestSolution   = "PA19";
GPIO8.associatedPins[2].pin.$suggestSolution = "PA16";
UART2.peripheral.$suggestSolution            = "UART1";

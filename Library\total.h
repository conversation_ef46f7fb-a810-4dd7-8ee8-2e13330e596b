#include "ti_msp_dl_config.h"
#include "stdio.h"
#include "stdlib.h"
#include "string.h"

#include "oled.h"
#include "button.h"
#include "interrupt.h"
#include "sensor.h"
#include "motor.h"
#include "PID.h"
#include "control.h"
#include "com.h"
#include "display.h"
#include "JY901.h"
#include "tracking.h"

// extern PalPID_HandleTypeDef hMotorASpeed_PID;   //motorA速度PID句柄声明
// extern PalPID_HandleTypeDef hMotorBSpeed_PID;   //motorB速度PID句柄声明
extern PalPID_HandleTypeDef hAngle_PID;            //角度环PID句柄声明
extern PalPID_HandleTypeDef hPosition_PID;         //位置环PID句柄声明


extern volatile int Mode;               //模式
extern volatile uint32_t current_time;  //系统当前时间
extern volatile int Car_Base_Speed;     //车速
extern volatile char str[30];           //OLED_ShowString()专用
extern volatile uint16_t TASK_OLED_DELAY;       //OLED刷新频率
extern volatile uint16_t TASK_BUTTON_DELAY;     //按键扫描频率
extern volatile uint16_t TASK_LED_DELAY;        //LED闪烁频率

extern volatile uint8_t buttonHandle_State;		//按键按下标志
extern volatile uint8_t buttonHandle_Record;    //按键按下状态记录
extern volatile uint32_t buttonHandle_Time;		//按键按下时间记录
extern volatile int g_iButton_State;            //按键键码
extern volatile int32_t g_MotorA_Encoder_Count, g_MotorB_Encoder_Count; //motorA和B编码器周期脉冲数
extern volatile float g_MotorA_Speed, g_MotorB_Speed;                   //motorA和B实时速度
extern volatile uint16_t button_position;      //菜单部件位置
extern volatile uint8_t g_UART0_Receive_Data[10];       //UART0接受数据储存
extern volatile uint8_t g_UART0_Receive_Data_Hex[5];    //UART0接收数据数字部分原始十六进制存储
extern volatile float g_UART0_Receive_Data_Float;       //UART0接收数据转换为浮点数存储
extern volatile bool UART0_received;                    //UART0接收完成标志位
extern volatile bool MotorSpeed_PIDControl_Flag;        //PID控制使能标志位
extern volatile bool Angle_PIDControl_Flag;             //角度环PID控制使能标志位
extern volatile bool Position_PIDControl_Flag;          //位置环PID控制使能标志位
extern volatile uint16_t display_Remind_flag;           //声光提醒标志位
extern volatile uint16_t display_OLED_mode;             //OLED显示模式

extern volatile uint8_t Uart1_Receive_Data;             //UART1接收到的数据
extern double wz;                                       //JY901解析角
extern volatile double Angle_Target;                    //目标角

// extern volatile bool ADC_Flag;
// extern volatile uint16_t ADC_Val1, ADC_Val2, ADC_Val3, ADC_Val4, ADC_Val5;
// extern volatile uint16_t D1, D2, D3, D4, D5;
// extern volatile uint16_t Digital_Threshold;
// extern volatile uint16_t B1_Max,B2_Max,B3_Max,B4_Max,B5_Max;
// extern volatile uint16_t sign;

// volatile int test_temp; 

extern volatile uint8_t H0, H1, H2, H3, H4, H5, H6;
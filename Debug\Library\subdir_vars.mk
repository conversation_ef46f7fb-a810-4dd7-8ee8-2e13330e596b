################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../Library/JY901.c \
../Library/PID.c \
../Library/button.c \
../Library/com.c \
../Library/control.c \
../Library/display.c \
../Library/interrupt.c \
../Library/motor.c \
../Library/oled.c \
../Library/sensor.c \
../Library/tracking.c 

C_DEPS += \
./Library/JY901.d \
./Library/PID.d \
./Library/button.d \
./Library/com.d \
./Library/control.d \
./Library/display.d \
./Library/interrupt.d \
./Library/motor.d \
./Library/oled.d \
./Library/sensor.d \
./Library/tracking.d 

OBJS += \
./Library/JY901.o \
./Library/PID.o \
./Library/button.o \
./Library/com.o \
./Library/control.o \
./Library/display.o \
./Library/interrupt.o \
./Library/motor.o \
./Library/oled.o \
./Library/sensor.o \
./Library/tracking.o 

OBJS__QUOTED += \
"Library\JY901.o" \
"Library\PID.o" \
"Library\button.o" \
"Library\com.o" \
"Library\control.o" \
"Library\display.o" \
"Library\interrupt.o" \
"Library\motor.o" \
"Library\oled.o" \
"Library\sensor.o" \
"Library\tracking.o" 

C_DEPS__QUOTED += \
"Library\JY901.d" \
"Library\PID.d" \
"Library\button.d" \
"Library\com.d" \
"Library\control.d" \
"Library\display.d" \
"Library\interrupt.d" \
"Library\motor.d" \
"Library\oled.d" \
"Library\sensor.d" \
"Library\tracking.d" 

C_SRCS__QUOTED += \
"../Library/JY901.c" \
"../Library/PID.c" \
"../Library/button.c" \
"../Library/com.c" \
"../Library/control.c" \
"../Library/display.c" \
"../Library/interrupt.c" \
"../Library/motor.c" \
"../Library/oled.c" \
"../Library/sensor.c" \
"../Library/tracking.c" 



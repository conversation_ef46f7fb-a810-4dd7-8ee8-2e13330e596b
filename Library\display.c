#include "total.h"

volatile char str[30];      //OLED_ShowString()专用
volatile uint16_t button_position = 1;      //菜单部件位置
volatile uint16_t display_Remind_flag;      //声光提醒标志位
volatile uint16_t display_OLED_mode;        //OLED显示模式
volatile uint32_t display_remind_time = 0;
volatile uint32_t display_remind_state = 0;


void display_OLED_Proc(int mode)
{
    if(mode == 0)   //Mode0菜单选择界面
    {
        OLED_ShowString(19, 0, (uint8_t*)"ChengFeng Car", 8);
        OLED_ShowString(0, 1, (uint8_t*)"____________________", 8);
        OLED_ShowString(8, 2, (uint8_t*)"MODE1", 8);
        OLED_ShowString(8, 3, (uint8_t*)"MODE2", 8);
        OLED_ShowString(8, 4, (uint8_t*)"MODE3", 8);
        OLED_ShowString(8, 5, (uint8_t*)"MODE4", 8);
        OLED_ShowString(8, 6, (uint8_t*)"JY901S", 8);
        OLED_ShowString(50, 7, (uint8_t*)"OK     Down", 8);
        if(button_position>1 && button_position<6)
        {
            OLED_ShowChar(0, button_position, ' ', 8);
        }
        if(button_position == 1)
        {
            OLED_ShowChar(0, 6, ' ', 8);
        }
        OLED_ShowChar(0, button_position + 1, '*', 8);
    }

    if(mode == 1)   //Mode1菜单选择界面
    {
        OLED_ShowString(50, 0, (uint8_t*)"MODE1", 8);
        OLED_ShowString(0, 1, (uint8_t*)"____________________", 8);
        sprintf(str, (uint8_t*)"R_Angle: %.2f",wz);
        OLED_ShowString(0, 2, (uint8_t*)str, 16);
        sprintf(str, "G_Angle: %.2f",Angle_Target);
        OLED_ShowString(0, 4, (uint8_t*)str, 16);
        OLED_ShowString(0, 7, (uint8_t*)"Go", 8);
        OLED_ShowString(55, 7, (uint8_t*)"Set", 8);
        OLED_ShowString(101, 7, (uint8_t*)"Back", 8);
    }

    if(mode == 2)   //Mode2菜单选择界面
    {
        OLED_ShowString(50, 0, (uint8_t*)"MODE2", 8);
        OLED_ShowString(0, 1, (uint8_t*)"____________________", 8);
        sprintf(str, "Real_Angle: %.2f",wz);
        OLED_ShowString(0, 2, (uint8_t*)str, 16);
        sprintf(str, "Goal_Angle: %.2f",Angle_Target);
        OLED_ShowString(0, 4, (uint8_t*)str, 16);
        OLED_ShowString(0, 7, (uint8_t*)"Go", 8);
        OLED_ShowString(55, 7, (uint8_t*)"Set", 8);
        OLED_ShowString(101, 7, (uint8_t*)"Back", 8);
    }

    if(mode == 3)   //Mode3菜单选择界面
    {
        OLED_ShowString(50, 0, (uint8_t*)"MODE3", 8);
        OLED_ShowString(0, 1, (uint8_t*)"____________________", 8);
        sprintf(str, "Real_Angle: %.2f",wz);
        OLED_ShowString(0, 2, (uint8_t*)str, 16);
        sprintf(str, "Goal_Angle: %.2f",Angle_Target);
        OLED_ShowString(0, 4, (uint8_t*)str, 16);
        OLED_ShowString(0, 7, (uint8_t*)"Go", 8);
        OLED_ShowString(55, 7, (uint8_t*)"Set", 8);
        OLED_ShowString(101, 7, (uint8_t*)"Back", 8);
    }

    if(mode == 4)   //Mode4菜单选择界面
    {
        OLED_ShowString(50, 0, (uint8_t*)"MODE4", 8);
        OLED_ShowString(0, 1, (uint8_t*)"____________________", 8);
        sprintf(str, "Real_Angle: %.2f",wz);
        OLED_ShowString(0, 2, (uint8_t*)str, 16);
        sprintf(str, "Goal_Angle: %.2f",Angle_Target);
        OLED_ShowString(0, 4, (uint8_t*)str, 16);
        OLED_ShowString(0, 7, (uint8_t*)"Go", 8);
        OLED_ShowString(55, 7, (uint8_t*)"Set", 8);
        OLED_ShowString(101, 7, (uint8_t*)"Back", 8);
    }

    if(mode == 5) //JY901S数据显示模式
    {
        OLED_ShowString(40, 0, (uint8_t*)"JY901S", 8);
        OLED_ShowString(0, 1, (uint8_t*)"____________________", 8);
        sprintf(str, "Angle: %.1f", wz);
        OLED_ShowString(0, 2, (uint8_t*)str, 8);
        sprintf(str, "Acc: %.2f", ax);
        OLED_ShowString(0, 3, (uint8_t*)str, 8);
        sprintf(str, "Temp: %.1f", temper);
        OLED_ShowString(0, 4, (uint8_t*)str, 8);
        sprintf(str, "Pos: %.2f,%.2f", px, py);
        OLED_ShowString(0, 5, (uint8_t*)str, 8);
        OLED_ShowString(0, 7, (uint8_t*)"Print    Back", 8);
    }

    if(mode == 10)
    {
        // sprintf(str, "%d %d %d %d %d",D1,D2,D3,D4,D5);
        // OLED_ShowString(0, 0, (uint8_t *)str, 8);
    }
}

void display_LED_Proc(int led_mode)
{
    if(led_mode == 11)
    {
        DL_GPIO_togglePins(LED_PORT, LED_LED1_PIN);
    }
}


void display_Remind_Proc(void)
{
    if(display_Remind_flag == 1)
    {
        if(display_remind_state == 0)
        {
            printf("current=%d\r\n",current_time);  //test
            display_remind_state = 1;
            display_remind_time = current_time;
            DL_GPIO_clearPins(LED_PORT, LED_LED1_PIN);
            DL_GPIO_setPins(BUZZER_PORT, BUZZER_BUZZER1_PIN);
        }
        if(current_time - display_remind_time > 1000)
        {
            printf("End=%d\r\n",current_time);  //test
            DL_GPIO_setPins(LED_PORT, LED_LED1_PIN);
            DL_GPIO_clearPins(BUZZER_PORT, BUZZER_BUZZER1_PIN);
            display_remind_state = 0;
            display_Remind_flag = 0;
        }
    }

    if(display_Remind_flag == 2)
    {
        if(display_remind_state == 0)
        {
            printf("current=%d\r\n",current_time);  //test
            display_remind_state = 1;
            display_remind_time = current_time;
            DL_GPIO_clearPins(LED_PORT, LED_LED1_PIN);
            DL_GPIO_setPins(BUZZER_PORT, BUZZER_BUZZER1_PIN);
        }
        if(current_time - display_remind_time > 500)
        {
            printf("End=%d\r\n",current_time);  //test
            DL_GPIO_setPins(LED_PORT, LED_LED1_PIN);
            DL_GPIO_clearPins(BUZZER_PORT, BUZZER_BUZZER1_PIN);
            display_remind_state = 0;
            display_Remind_flag = 0;
        }
    }
}

// void dispaly_OLED_Proc(int oled_mode)
// {
//     if(oled_mode == 11)
//     {
//         display_OLED_SpeedPID_Mode();
//     }
// }

// void display_OLED_SpeedPID_Mode(void)
// {
//     //标题栏
//     OLED_ShowString(40, 0, (uint8_t*)"MotorPID", 8);
//     OLED_ShowString(0, 1, (uint8_t*)"____________________", 8);
//     //速度显示栏
//     sprintf(str,"A:%3.1f",g_MotorA_Speed);
//     OLED_ShowString(0, 2, (uint8_t*)str, 16);
//     sprintf(str,"B:%3.1f",g_MotorB_Speed);
//     OLED_ShowString(64, 2, (uint8_t*)str, 16);
//     //A电机PID参数显示栏
//     sprintf(str,"P:%4d",(int)hMotorASpeed_PID.Kp);
//     OLED_ShowString(7, 4, (uint8_t*)str, 8);
//     sprintf(str,"I:%4d",(int)hMotorASpeed_PID.Ki);
//     OLED_ShowString(7, 5, (uint8_t*)str, 8);
//     sprintf(str,"D:%4d",(int)hMotorASpeed_PID.Kd);
//     OLED_ShowString(7, 6, (uint8_t*)str, 8);
//     //B电机PID参数显示栏
//     sprintf(str,"P:%4d",(int)hMotorBSpeed_PID.Kp);
//     OLED_ShowString(71, 4, (uint8_t*)str, 8);
//     sprintf(str,"I:%4d",(int)hMotorBSpeed_PID.Ki);
//     OLED_ShowString(71, 5, (uint8_t*)str, 8);
//     sprintf(str,"D:%4d",(int)hMotorBSpeed_PID.Kd);
//     OLED_ShowString(71, 6, (uint8_t*)str, 8);
//     //菜单部件显示
//     OLED_ShowChar(64*button_position-32, 7, '*', 8);
//     OLED_ShowChar(-64*button_position+160, 7, ' ', 8);
// }
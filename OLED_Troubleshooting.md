# OLED 故障排除指南 - CCS Theia

## 问题现象
OLED屏幕无任何显示，完全黑屏

## 可能原因及解决方案

### 1. 硬件连接问题 ⚡
**检查清单：**
- [ ] VCC 连接到 3.3V（不是5V！）
- [ ] GND 连接到地线
- [ ] SDA 连接到 PA11
- [ ] SCL 连接到 PA12
- [ ] 连接线是否松动或断开
- [ ] OLED模块是否损坏

**测试方法：**
```c
// 运行程序后查看串口输出
// 应该看到：
// "Device found at address: 0x3C"
// "OLED Device Found at 0x3C"
```

### 2. I2C配置问题 🔧
**检查项目：**
- I2C地址：0x3C（7位地址）
- I2C速度：Fast Mode (400kHz)
- 引脚配置：PA11(SDA), PA12(SCL)

**验证方法：**
1. 查看串口输出的I2C扫描结果
2. 确认没有"I2C Timeout"错误信息

### 3. 电源问题 🔋
**常见问题：**
- OLED模块需要3.3V供电，不能用5V
- 电流不足（某些OLED需要较大电流）
- 电源纹波过大

**解决方案：**
- 使用万用表测量VCC电压
- 尝试外部3.3V电源供电
- 添加去耦电容（100nF + 10uF）

### 4. CCS Theia特定问题 💻
**可能原因：**
- 项目配置未正确生成
- 编译优化问题
- 调试器干扰

**解决步骤：**
1. 清理并重新编译项目
2. 检查main.syscfg配置
3. 确认ti_msp_dl_config.h已生成
4. 尝试Release模式编译

### 5. 代码执行问题 🐛
**检查点：**
- main()函数是否正常执行
- OLED_Init()是否被调用
- 是否有死循环阻塞程序

**调试方法：**
```c
// 在关键位置添加printf调试
printf("Before OLED_Init\r\n");
OLED_Init();
printf("After OLED_Init\r\n");
```

## 调试步骤

### 第一步：检查串口输出
运行程序后，串口应该输出：
```
=== OLED Diagnostic Start ===
I2C OLED Address: 0x3C
SDA Pin: PA11, SCL Pin: PA12
Scanning I2C bus...
Device found at address: 0x3C
I2C scan complete.
OLED Device Found at 0x3C
```

### 第二步：硬件测试
如果没有找到设备：
1. 检查所有连接线
2. 用万用表测量电压
3. 尝试不同的OLED模块

### 第三步：软件测试
如果找到设备但无显示：
1. 检查初始化序列
2. 尝试简化的初始化
3. 手动发送显示命令

### 第四步：替代方案
如果仍无法解决：
1. 尝试SPI接口的OLED
2. 使用逻辑分析仪检查I2C信号
3. 参考TI官方I2C示例

## 常见错误信息

| 错误信息 | 原因 | 解决方案 |
|---------|------|----------|
| "No I2C devices found!" | 硬件连接问题 | 检查连接线和电源 |
| "I2C Timeout!" | I2C通信失败 | 检查引脚配置和时钟 |
| "OLED Device NOT Found" | 设备地址错误 | 确认OLED地址为0x3C |

## 联系支持
如果以上方法都无法解决问题，请提供：
1. 串口完整输出
2. 硬件连接照片
3. 项目配置文件
4. OLED模块型号

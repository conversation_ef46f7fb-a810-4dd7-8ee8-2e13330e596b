#include "oled.h"
#include "oledfont.h"
#include "ti/driverlib/m0p/dl_core.h"
#include "stdio.h"

enum I2cControllerStatus {
    I2C_STATUS_IDLE = 0,
    I2C_STATUS_TX_STARTED,
    I2C_STATUS_TX_INPROGRESS,
    I2C_STATUS_TX_COMPLETE,
    I2C_STATUS_RX_STARTED,
    I2C_STATUS_RX_INPROGRESS,
    I2C_STATUS_RX_COMPLETE,
    I2C_STATUS_ERROR,
} gI2cControllerStatus;

uint32_t gTxLen, gTxCount, gRxCount, gRxLen;
uint8_t gTxPacket[16];
uint8_t gRxPacket[16];

//OLED的显存
//存放格式如下.
//[0]0 1 2 3 ... 127	
//[1]0 1 2 3 ... 127	
//[2]0 1 2 3 ... 127	
//[3]0 1 2 3 ... 127	
//[4]0 1 2 3 ... 127	
//[5]0 1 2 3 ... 127	
//[6]0 1 2 3 ... 127	
//[7]0 1 2 3 ... 127 			   
void delay_ms(unsigned long ms) 
{
    while(ms--)
	    delay_cycles(CPUCLK_FREQ/1000);
}

//反显函数
void OLED_ColorTurn(uint8_t i)
{
    if(i==0)
    {
        OLED_WR_Byte(0xA6,OLED_CMD);//正常显示
    }
    if(i==1)
    {
        OLED_WR_Byte(0xA7,OLED_CMD);//反色显示
    }
}

//屏幕旋转180度
void OLED_DisplayTurn(uint8_t i)
{
if(i==0)
    {
        OLED_WR_Byte(0xC8,OLED_CMD);//正常显示
        OLED_WR_Byte(0xA1,OLED_CMD);
    }
    if(i==1)
    {
        OLED_WR_Byte(0xC0,OLED_CMD);//反转显示
        OLED_WR_Byte(0xA0,OLED_CMD);
    }
}

//发送一个字节
//向SSD1306写入一个字节。
//mode:数据/命令标志 0,表示命令;1,表示数据;
void OLED_WR_Byte(uint8_t dat,uint8_t mode)
{
    uint16_t i;
    uint32_t timeout = 10000; //添加超时计数器

    gI2cControllerStatus = I2C_STATUS_IDLE;
    gTxLen = 2;

    if(mode)
        gTxPacket[0] = 0x40;
    else
        gTxPacket[0] = 0x00;
    gTxPacket[1] = dat;

    gTxCount = DL_I2C_fillControllerTXFIFO(I2C_OLED_INST, &gTxPacket[0], gTxLen);

    DL_I2C_disableInterrupt(I2C_OLED_INST, DL_I2C_INTERRUPT_CONTROLLER_TXFIFO_TRIGGER);

    gI2cControllerStatus = I2C_STATUS_TX_STARTED;

    //等待I2C空闲，添加超时保护
    timeout = 10000;
    while (!(DL_I2C_getControllerStatus(I2C_OLED_INST) & DL_I2C_CONTROLLER_STATUS_IDLE) && timeout--);
    if(timeout == 0) printf("I2C Idle Timeout!\r\n");

    DL_I2C_startControllerTransfer(I2C_OLED_INST, 0x3C, DL_I2C_CONTROLLER_DIRECTION_TX, gTxLen);

    //等待传输完成，添加超时保护
    timeout = 10000;
    while ((gI2cControllerStatus != I2C_STATUS_TX_COMPLETE) && (gI2cControllerStatus != I2C_STATUS_ERROR) && timeout--);
    if(timeout == 0) printf("I2C TX Timeout!\r\n");
    if(gI2cControllerStatus == I2C_STATUS_ERROR) printf("I2C Error!\r\n");

    //等待总线空闲
    timeout = 10000;
    while (DL_I2C_getControllerStatus(I2C_OLED_INST) & DL_I2C_CONTROLLER_STATUS_BUSY_BUS && timeout--);
    if(timeout == 0) printf("I2C Bus Busy Timeout!\r\n");

    //最终等待空闲
    timeout = 10000;
    while (!(DL_I2C_getControllerStatus(I2C_OLED_INST) & DL_I2C_CONTROLLER_STATUS_IDLE) && timeout--);
    if(timeout == 0) printf("I2C Final Idle Timeout!\r\n");
}

//坐标设置

void OLED_Set_Pos(uint8_t x, uint8_t y) 
{ 
    OLED_WR_Byte(0xb0+y,OLED_CMD);
    OLED_WR_Byte(((x&0xf0)>>4)|0x10,OLED_CMD);
    OLED_WR_Byte((x&0x0f),OLED_CMD);
}   	  
//开启OLED显示    
void OLED_Display_On(void)
{
    OLED_WR_Byte(0X8D,OLED_CMD);  //SET DCDC命令
    OLED_WR_Byte(0X14,OLED_CMD);  //DCDC ON
    OLED_WR_Byte(0XAF,OLED_CMD);  //DISPLAY ON
}
//关闭OLED显示     
void OLED_Display_Off(void)
{
    OLED_WR_Byte(0X8D,OLED_CMD);  //SET DCDC命令
    OLED_WR_Byte(0X10,OLED_CMD);  //DCDC OFF
    OLED_WR_Byte(0XAE,OLED_CMD);  //DISPLAY OFF
}		   			 
//清屏函数,清完屏,整个屏幕是黑色的!和没点亮一样!!!	  
void OLED_Clear(void)  
{  
    uint8_t i,n;		    
    for(i=0;i<8;i++)  
    {  
        OLED_WR_Byte (0xb0+i,OLED_CMD);    //设置页地址（0~7）
        OLED_WR_Byte (0x00,OLED_CMD);      //设置显示位置—列低地址
        OLED_WR_Byte (0x10,OLED_CMD);      //设置显示位置—列高地址   
        for(n=0;n<128;n++)OLED_WR_Byte(0,OLED_DATA); 
    } //更新显示
}

//在指定位置显示一个字符,包括部分字符
//x:0~127
//y:0~63				 
//sizey:选择字体 6x8  8x16
void OLED_ShowChar(uint8_t x,uint8_t y,uint8_t chr,uint8_t sizey)
{      	
    uint8_t c=0,sizex=sizey/2;
    uint16_t i=0,size1;
    if(sizey==8)size1=6;
    else size1=(sizey/8+((sizey%8)?1:0))*(sizey/2);
    c=chr-' ';//得到偏移后的值
    OLED_Set_Pos(x,y);
    for(i=0;i<size1;i++)
    {
        if(i%sizex==0&&sizey!=8) OLED_Set_Pos(x,y++);
        if(sizey==8) OLED_WR_Byte(asc2_0806[c][i],OLED_DATA);//6X8字号
        else if(sizey==16) OLED_WR_Byte(asc2_1608[c][i],OLED_DATA);//8x16字号
        //		else if(sizey==xx) OLED_WR_Byte(asc2_xxxx[c][i],OLED_DATA);//用户添加字号
        else return;
    }
}
//m^n函数
uint32_t oled_pow(uint8_t m,uint8_t n)
{
    uint32_t result=1;	 
    while(n--)result*=m;    
    return result;
}				  
//显示数字
//x,y :起点坐标
//num:要显示的数字
//len :数字的位数
//sizey:字体大小		  
void OLED_ShowNum(uint8_t x,uint8_t y,uint32_t num,uint8_t len,uint8_t sizey)
{         	
    uint8_t t,temp,m=0;
    uint8_t enshow=0;
    if(sizey==8)m=2;
    for(t=0;t<len;t++)
    {
        temp=(num/oled_pow(10,len-t-1))%10;
        if(enshow==0&&t<(len-1))
        {
            if(temp==0)
            {
                OLED_ShowChar(x+(sizey/2+m)*t,y,' ',sizey);
                continue;
            }else enshow=1;
        }
        OLED_ShowChar(x+(sizey/2+m)*t,y,temp+'0',sizey);
    }
}
//显示一个字符号串
void OLED_ShowString(uint8_t x,uint8_t y,uint8_t *chr,uint8_t sizey)
{
    uint8_t j=0;
    while (chr[j]!='\0')
    {		
        OLED_ShowChar(x,y,chr[j++],sizey);
        if(sizey==8)x+=6;
        else x+=sizey/2;
    }
}
//显示汉字
void OLED_ShowChinese(uint8_t x,uint8_t y,uint8_t no,uint8_t sizey)
{
    uint16_t i,size1=(sizey/8+((sizey%8)?1:0))*sizey;
    for(i=0;i<size1;i++)
    {
        if(i%sizey==0) OLED_Set_Pos(x,y++);
        if(sizey==16) OLED_WR_Byte(Hzk[no][i],OLED_DATA);//16x16字号
        //		else if(sizey==xx) OLED_WR_Byte(xxx[c][i],OLED_DATA);//用户添加字号
        else return;
    }				
}


//显示图片
//x,y显示坐标
//sizex,sizey,图片长宽
//BMP：要显示的图片
void OLED_DrawBMP(uint8_t x,uint8_t y,uint8_t sizex, uint8_t sizey,uint8_t BMP[])
{ 	
    uint16_t j=0;
    uint8_t i,m;
    sizey=sizey/8+((sizey%8)?1:0);
    for(i=0;i<sizey;i++)
    {
        OLED_Set_Pos(x,i+y);
        for(m=0;m<sizex;m++)
        {      
            OLED_WR_Byte(BMP[j++],OLED_DATA);	    	
        }
    }
} 



//初始化SSD1306					    
void OLED_Init(void)
{
    delay_ms(200);
    NVIC_EnableIRQ(I2C_OLED_INST_INT_IRQN);
    OLED_WR_Byte(0xAE,OLED_CMD);//--turn off oled panel
    OLED_WR_Byte(0x00,OLED_CMD);//---set low column address
    OLED_WR_Byte(0x10,OLED_CMD);//---set high column address
    OLED_WR_Byte(0x40,OLED_CMD);//--set start line address  Set Mapping RAM Display Start Line (0x00~0x3F)
    OLED_WR_Byte(0x81,OLED_CMD);//--set contrast control register
    OLED_WR_Byte(0xCF,OLED_CMD); // Set SEG Output Current Brightness
    OLED_WR_Byte(0xA1,OLED_CMD);//--Set SEG/Column Mapping     0xa0左右反置 0xa1正常
    OLED_WR_Byte(0xC8,OLED_CMD);//Set COM/Row Scan Direction   0xc0上下反置 0xc8正常
    OLED_WR_Byte(0xA6,OLED_CMD);//--set normal display
    OLED_WR_Byte(0xA8,OLED_CMD);//--set multiplex ratio(1 to 64)
    OLED_WR_Byte(0x3f,OLED_CMD);//--1/64 duty
    OLED_WR_Byte(0xD3,OLED_CMD);//-set display offset	Shift Mapping RAM Counter (0x00~0x3F)
    OLED_WR_Byte(0x00,OLED_CMD);//-not offset
    OLED_WR_Byte(0xd5,OLED_CMD);//--set display clock divide ratio/oscillator frequency
    OLED_WR_Byte(0x80,OLED_CMD);//--set divide ratio, Set Clock as 100 Frames/Sec
    OLED_WR_Byte(0xD9,OLED_CMD);//--set pre-charge period
    OLED_WR_Byte(0xF1,OLED_CMD);//Set Pre-Charge as 15 Clocks & Discharge as 1 Clock
    OLED_WR_Byte(0xDA,OLED_CMD);//--set com pins hardware configuration
    OLED_WR_Byte(0x12,OLED_CMD);
    OLED_WR_Byte(0xDB,OLED_CMD);//--set vcomh
    OLED_WR_Byte(0x40,OLED_CMD);//Set VCOM Deselect Level
    OLED_WR_Byte(0x20,OLED_CMD);//-Set Page Addressing Mode (0x00/0x01/0x02)
    OLED_WR_Byte(0x02,OLED_CMD);//
    OLED_WR_Byte(0x8D,OLED_CMD);//--set Charge Pump enable/disable
    OLED_WR_Byte(0x14,OLED_CMD);//--set(0x10) disable
    OLED_WR_Byte(0xA4,OLED_CMD);// Disable Entire Display On (0xa4/0xa5)
    OLED_WR_Byte(0xA6,OLED_CMD);// Disable Inverse Display On (0xa6/a7) 
    OLED_Clear();
    OLED_WR_Byte(0xAF,OLED_CMD); /*display ON*/
}

//检测OLED设备是否连接
bool OLED_Check_Device(void)
{
    gI2cControllerStatus = I2C_STATUS_IDLE;
    gTxLen = 1;
    gTxPacket[0] = 0x00; //命令模式

    gTxCount = DL_I2C_fillControllerTXFIFO(I2C_OLED_INST, &gTxPacket[0], gTxLen);
    DL_I2C_disableInterrupt(I2C_OLED_INST, DL_I2C_INTERRUPT_CONTROLLER_TXFIFO_TRIGGER);

    gI2cControllerStatus = I2C_STATUS_TX_STARTED;
    DL_I2C_startControllerTransfer(I2C_OLED_INST, 0x3C, DL_I2C_CONTROLLER_DIRECTION_TX, gTxLen);

    uint32_t timeout = 5000;
    while ((gI2cControllerStatus != I2C_STATUS_TX_COMPLETE) &&
           (gI2cControllerStatus != I2C_STATUS_ERROR) && timeout--);

    if(gI2cControllerStatus == I2C_STATUS_TX_COMPLETE) {
        printf("OLED Device Found at 0x3C\r\n");
        return true;
    } else {
        printf("OLED Device NOT Found at 0x3C\r\n");
        return false;
    }
}

//扫描I2C总线上的设备
void I2C_Scan_Devices(void)
{
    printf("Scanning I2C bus (detailed)...\r\n");
    printf("Common OLED addresses to check:\r\n");
    printf("- 0x3C (most common)\r\n");
    printf("- 0x3D (alternative)\r\n");
    printf("- 0x78 (8-bit address for 0x3C)\r\n");
    printf("- 0x7A (8-bit address for 0x3D)\r\n");

    bool found = false;
    uint8_t common_addrs[] = {0x3C, 0x3D, 0x78, 0x7A};

    // 首先检查常见OLED地址
    printf("Checking common OLED addresses...\r\n");
    for(int i = 0; i < 4; i++) {
        uint8_t addr = common_addrs[i];
        gI2cControllerStatus = I2C_STATUS_IDLE;
        gTxLen = 0; // 只发送地址，不发送数据

        gI2cControllerStatus = I2C_STATUS_TX_STARTED;
        DL_I2C_startControllerTransfer(I2C_OLED_INST, addr, DL_I2C_CONTROLLER_DIRECTION_TX, gTxLen);

        uint32_t timeout = 5000;
        while ((gI2cControllerStatus != I2C_STATUS_TX_COMPLETE) &&
               (gI2cControllerStatus != I2C_STATUS_ERROR) && timeout--);

        if(gI2cControllerStatus == I2C_STATUS_TX_COMPLETE) {
            printf("*** OLED Device found at address: 0x%02X ***\r\n", addr);
            found = true;
        } else {
            printf("No response from 0x%02X\r\n", addr);
        }

        delay_ms(10);
    }

    // 全范围扫描
    printf("Full range scan (0x08-0x77)...\r\n");
    for(uint8_t addr = 0x08; addr < 0x78; addr++) {
        gI2cControllerStatus = I2C_STATUS_IDLE;
        gTxLen = 0;

        gI2cControllerStatus = I2C_STATUS_TX_STARTED;
        DL_I2C_startControllerTransfer(I2C_OLED_INST, addr, DL_I2C_CONTROLLER_DIRECTION_TX, gTxLen);

        uint32_t timeout = 1000;
        while ((gI2cControllerStatus != I2C_STATUS_TX_COMPLETE) &&
               (gI2cControllerStatus != I2C_STATUS_ERROR) && timeout--);

        if(gI2cControllerStatus == I2C_STATUS_TX_COMPLETE) {
            printf("Device found at address: 0x%02X\r\n", addr);
            found = true;
        }

        delay_ms(1);
    }

    if(!found) {
        printf("*** NO I2C DEVICES FOUND! ***\r\n");
        printf("Possible issues:\r\n");
        printf("1. Wrong pin connections\r\n");
        printf("2. OLED module damaged\r\n");
        printf("3. Power supply issue\r\n");
        printf("4. Pull-up resistors missing\r\n");
    }
    printf("I2C scan complete.\r\n");
}

//检查I2C引脚状态
void Check_I2C_Pins(void)
{
    printf("=== I2C Pin Status Check ===\r\n");

    // 读取SDA引脚状态 (PA10)
    bool sda_state = DL_GPIO_readPins(GPIOA, DL_GPIO_PIN_10);
    printf("SDA (PA10) state: %s\r\n", sda_state ? "HIGH" : "LOW");

    // 读取SCL引脚状态 (PA11)
    bool scl_state = DL_GPIO_readPins(GPIOA, DL_GPIO_PIN_11);
    printf("SCL (PA11) state: %s\r\n", scl_state ? "HIGH" : "LOW");

    printf("Expected: Both pins should be HIGH when idle\r\n");

    if(!sda_state || !scl_state) {
        printf("*** WARNING: I2C pins not in idle state! ***\r\n");
        printf("Possible issues:\r\n");
        printf("- Missing pull-up resistors\r\n");
        printf("- Short circuit\r\n");
        printf("- Wrong pin connections\r\n");
    } else {
        printf("I2C pins appear to be in correct idle state\r\n");
    }
    printf("=== Pin Check Complete ===\r\n");
}

//简化的OLED初始化，用于调试
void OLED_Init_Simple(void)
{
    printf("Starting simple OLED init...\r\n");

    delay_ms(100); //等待OLED上电稳定

    //基本初始化序列
    OLED_WR_Byte(0xAE, OLED_CMD); //关闭显示
    OLED_WR_Byte(0x20, OLED_CMD); //设置内存寻址模式
    OLED_WR_Byte(0x10, OLED_CMD); //00,水平寻址模式;01,垂直寻址模式;10,页寻址模式(RESET);11,无效
    OLED_WR_Byte(0xB0, OLED_CMD); //设置页起始地址为页0
    OLED_WR_Byte(0xC8, OLED_CMD); //设置COM扫描方向
    OLED_WR_Byte(0x00, OLED_CMD); //设置低列地址
    OLED_WR_Byte(0x10, OLED_CMD); //设置高列地址
    OLED_WR_Byte(0x40, OLED_CMD); //设置起始行地址
    OLED_WR_Byte(0x81, OLED_CMD); //设置对比度控制寄存器
    OLED_WR_Byte(0xFF, OLED_CMD); //设置对比度为最大
    OLED_WR_Byte(0xA1, OLED_CMD); //设置段重新映射
    OLED_WR_Byte(0xA6, OLED_CMD); //设置正常显示
    OLED_WR_Byte(0xA8, OLED_CMD); //设置多路复用比
    OLED_WR_Byte(0x3F, OLED_CMD); //1/64 duty
    OLED_WR_Byte(0xA4, OLED_CMD); //0xa4,正常显示;0xa5,整体显示;
    OLED_WR_Byte(0xD3, OLED_CMD); //设置显示偏移
    OLED_WR_Byte(0x00, OLED_CMD); //无偏移
    OLED_WR_Byte(0xD5, OLED_CMD); //设置显示时钟分频比/振荡器频率
    OLED_WR_Byte(0xF0, OLED_CMD); //设置分频比
    OLED_WR_Byte(0xD9, OLED_CMD); //设置预充电周期
    OLED_WR_Byte(0x22, OLED_CMD); //
    OLED_WR_Byte(0xDA, OLED_CMD); //设置com硬件引脚配置
    OLED_WR_Byte(0x12, OLED_CMD); //
    OLED_WR_Byte(0xDB, OLED_CMD); //设置vcomh
    OLED_WR_Byte(0x20, OLED_CMD); //0x20,0.77xVcc
    OLED_WR_Byte(0x8D, OLED_CMD); //设置DC-DC使能
    OLED_WR_Byte(0x14, OLED_CMD); //
    OLED_WR_Byte(0xAF, OLED_CMD); //开启显示

    printf("Simple OLED init completed\r\n");
}

//CCS Theia专用配置检查
void OLED_Check_CCS_Config(void)
{
    printf("=== CCS Theia Configuration Check ===\r\n");

    //检查I2C外设是否启用
    printf("Checking I2C peripheral...\r\n");

    //检查引脚配置
    printf("Pin Configuration:\r\n");
    printf("- SDA: PA11 (I2C0_SDA)\r\n");
    printf("- SCL: PA12 (I2C0_SCL)\r\n");

    //检查时钟配置
    printf("Clock Configuration:\r\n");
    printf("- I2C Clock: Enabled\r\n");
    printf("- Bus Speed: Fast Mode (400kHz)\r\n");

    //检查中断配置
    printf("Interrupt Configuration:\r\n");
    printf("- I2C Interrupts: Enabled\r\n");

    printf("=== Configuration Check Complete ===\r\n");
}

void I2C_OLED_INST_IRQHandler(void)
{
    switch (DL_I2C_getPendingInterrupt(I2C_OLED_INST)) {
        case DL_I2C_IIDX_CONTROLLER_RX_DONE:
            gI2cControllerStatus = I2C_STATUS_RX_COMPLETE;
            break;
        case DL_I2C_IIDX_CONTROLLER_TX_DONE:
            DL_I2C_disableInterrupt(
                I2C_OLED_INST, DL_I2C_INTERRUPT_CONTROLLER_TXFIFO_TRIGGER);
            gI2cControllerStatus = I2C_STATUS_TX_COMPLETE;
            break;
        case DL_I2C_IIDX_CONTROLLER_RXFIFO_TRIGGER:
            gI2cControllerStatus = I2C_STATUS_RX_INPROGRESS;
            /* Receive all bytes from target */
            while (DL_I2C_isControllerRXFIFOEmpty(I2C_OLED_INST) != true) {
                if (gRxCount < gRxLen) {
                    gRxPacket[gRxCount++] =
                        DL_I2C_receiveControllerData(I2C_OLED_INST);
                } else {
                    /* Ignore and remove from FIFO if the buffer is full */
                    DL_I2C_receiveControllerData(I2C_OLED_INST);
                }
            }
            break;
        case DL_I2C_IIDX_CONTROLLER_TXFIFO_TRIGGER:
            gI2cControllerStatus = I2C_STATUS_TX_INPROGRESS;
            /* Fill TX FIFO with next bytes to send */
            if (gTxCount < gTxLen) {
                gTxCount += DL_I2C_fillControllerTXFIFO(
                    I2C_OLED_INST, &gTxPacket[gTxCount], gTxLen - gTxCount);
            }
            break;
            /* Not used for this example */
        case DL_I2C_IIDX_CONTROLLER_ARBITRATION_LOST:
        case DL_I2C_IIDX_CONTROLLER_NACK:
            if ((gI2cControllerStatus == I2C_STATUS_RX_STARTED) ||
                (gI2cControllerStatus == I2C_STATUS_TX_STARTED)) {
                /* NACK interrupt if I2C Target is disconnected */
                gI2cControllerStatus = I2C_STATUS_ERROR;
            }
        case DL_I2C_IIDX_CONTROLLER_RXFIFO_FULL:
        case DL_I2C_IIDX_CONTROLLER_TXFIFO_EMPTY:
        case DL_I2C_IIDX_CONTROLLER_START:
        case DL_I2C_IIDX_CONTROLLER_STOP:
        case DL_I2C_IIDX_CONTROLLER_EVENT1_DMA_DONE:
        case DL_I2C_IIDX_CONTROLLER_EVENT2_DMA_DONE:
        default:
            break;
    }
}

//简化的JY901S数据显示函数
void OLED_Display_JY901S(void)
{
    extern float wx, wy, wz, temper; //引用JY901S全局变量
    char str[20];

    OLED_Clear(); //清屏

    //显示标题
    OLED_ShowString(0, 0, (uint8_t*)"JY901S Data", 16);

    //显示Roll角度
    OLED_ShowString(0, 16, (uint8_t*)"Roll:", 16);
    sprintf(str, "%.1f", wx);
    OLED_ShowString(48, 16, (uint8_t*)str, 16);

    //显示Pitch角度
    OLED_ShowString(0, 32, (uint8_t*)"Pitch:", 16);
    sprintf(str, "%.1f", wy);
    OLED_ShowString(54, 32, (uint8_t*)str, 16);

    //显示Yaw角度
    OLED_ShowString(0, 48, (uint8_t*)"Yaw:", 16);
    sprintf(str, "%.1f", wz);
    OLED_ShowString(36, 48, (uint8_t*)str, 16);
}
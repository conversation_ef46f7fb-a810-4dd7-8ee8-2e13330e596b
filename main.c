#include "ti/driverlib/m0p/dl_core.h"
#include "total.h"
#include "stdio.h"

//简化的JY901S OLED显示系统
volatile int Mode = 5; //固定为JY901S显示模式
volatile uint32_t current_time;                   //系统当前时间
volatile uint16_t TASK_OLED_DELAY = 200;          //OLED刷新频率
volatile uint16_t TASK_BUTTON_DELAY = 5;          //按键扫描频率
volatile uint16_t display_OLED_mode = 5;          //OLED显示模式
volatile int Car_Base_Speed = 0;                  //小车基本速度
volatile double Angle_Target = 0;                 //目标角


PalPID_HandleTypeDef hAngle_PID;             //角度环PID句柄声明
PalPID_HandleTypeDef hPosition_PID;          //位置环PID句柄声明





int main(void)
{
    
    SYSCFG_DL_init();       //芯片初始化

    // 立即输出启动信息
    printf("*** SYSTEM STARTUP ***\r\n");
    printf("ChengFengCar v2.0 Starting...\r\n");
    printf("UART0 Debug: 115200 baud, PA28=TX, PA31=RX\r\n");

    DL_TimerG_startCounter(Motor_PWM_INST);             //电机PWM使能
    NVIC_EnableIRQ(TIMER_Encoder_Read_INST_INT_IRQN);   //编码器定时器使能
    NVIC_EnableIRQ(GPIO_MULTIPLE_GPIOA_INT_IRQN);       //GPIOA中断使能
    DL_Timer_startCounter(TIMER_Encoder_Read_INST);     //开启编码器定时器计时
    NVIC_ClearPendingIRQ(UART_0_INST_INT_IRQN);         //清除UART0中断标志位
    NVIC_EnableIRQ(UART_0_INST_INT_IRQN);               //UART0中断使能
    NVIC_ClearPendingIRQ(UART_1_INST_INT_IRQN);         //清除UART1中断标志位
    NVIC_EnableIRQ(UART_1_INST_INT_IRQN);               //UART1中断使能
    // NVIC_EnableIRQ(ADC12_1_INST_INT_IRQN);              //ADC中断使能


    //OLED初始化
    printf("Starting OLED initialization...\r\n");
    OLED_Init();                 //硬件I2C-OLED初始化
    delay_ms(200);
    OLED_Clear();                //OLED清屏
    delay_ms(100);
    control_PID_Init();          //PID控制器初始化
    motor_Direction_Set(0,0);    //电机初始化
    // tracking_Init();             //循迹初始化


    /*任务系统时间初始化*/
    current_time = Get_Tick();
    uint32_t last_task01_time = current_time;
	uint32_t last_task02_time = current_time;
    

    Mode = 5;   //固定为JY901S显示模式

    //简化初始化
    printf("*** JY901S OLED Display System ***\r\n");
    printf("Initializing OLED...\r\n");

    //初始化OLED
    OLED_Init();
    OLED_Clear();

    //显示启动信息
    OLED_ShowString(0, 0, (uint8_t*)"JY901S System", 16);
    OLED_ShowString(0, 16, (uint8_t*)"Starting...", 16);
    delay_ms(2000);

    printf("System ready. Displaying JY901S data...\r\n");
    while (1)
    {
        current_time = Get_Tick();

        /*OLED显示JY901S数据*/
        if(current_time - last_task02_time >= TASK_OLED_DELAY)
        {
            last_task02_time = current_time;

            //直接在这里显示JY901S数据，避免函数调用问题
            extern float wx, wy, wz, temper;
            char str[20];

            OLED_Clear();
            OLED_ShowString(0, 0, (uint8_t*)"JY901S Data", 16);

            OLED_ShowString(0, 16, (uint8_t*)"Roll:", 16);
            sprintf(str, "%.1f", wx);
            OLED_ShowString(48, 16, (uint8_t*)str, 16);

            OLED_ShowString(0, 32, (uint8_t*)"Pitch:", 16);
            sprintf(str, "%.1f", wy);
            OLED_ShowString(54, 32, (uint8_t*)str, 16);

            OLED_ShowString(0, 48, (uint8_t*)"Yaw:", 16);
            sprintf(str, "%.1f", wz);
            OLED_ShowString(36, 48, (uint8_t*)str, 16);
        }

        delay_ms(10); //减少CPU占用
    }
}


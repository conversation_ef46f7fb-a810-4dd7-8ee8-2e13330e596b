#include "ti/driverlib/m0p/dl_core.h"
#include "total.h"          //头文件合集
#include "stdio.h"          

volatile int Mode = 0;
/*
    -功能模式说明-
    Mode = 0 ：待机模式，OLED打印各模块初始化及自检信息，LED快速闪烁，确认后进入菜单，
                        OLED显示菜单，OLED刷新、按键扫描频率快
    Mode = 1 ：第一题，小车朝正北放置在A点，按下按键，小车自动直线行驶至B点，蜂鸣器长鸣、LED长亮一秒
    Mode = 2 ：第二题，小车朝正北放置在A点，按下按键，小车自动直线行驶至B点，蜂鸣器短鸣、LED闪烁一次；
                        随后模拟量红外循弧线至C点，蜂鸣器短鸣、LED闪烁一次；随后自动直线行驶至D点，
                        蜂鸣器短鸣、LED闪烁一次；最后模拟量红外循弧线至C点，蜂鸣器长鸣、LED长亮一秒
    Mode = 3 ：第三题，小车朝正北放置在A点，按下按键，小车自动调整角度至东偏北50°并自动斜线行驶至C点，
                        蜂鸣器短鸣、LED闪烁一次；随后模拟量红外循线至B点，蜂鸣器短鸣、LED闪烁一次；
                        随后小车自动调整角度至西偏南50°并自动斜线行驶至D点，蜂鸣器短鸣、LED闪烁一次；
                        最后模拟量红外循迹至A点，蜂鸣器长鸣、LED长亮一秒
    Mode = 4 ：第四题，重复Mode4四圈并自动停车，蜂鸣器长鸣、LED快速闪烁
    Mode = 11：测速模式1，OLED显示PID与电机转速、按键扫描频率快，LED每800ms亮灭翻转
*/

volatile uint32_t current_time;                   //系统当前时间

volatile uint16_t TASK_OLED_DELAY = 200;          //OLED刷新频率
volatile uint16_t TASK_BUTTON_DELAY = 5;          //按键扫描频率


PalPID_HandleTypeDef hAngle_PID;             //角度环PID句柄声明
PalPID_HandleTypeDef hPosition_PID;          //位置环PID句柄声明



int main(void)
{
    
    SYSCFG_DL_init();       //芯片初始化

    DL_TimerG_startCounter(Motor_PWM_INST);             //电机PWM使能
    NVIC_EnableIRQ(TIMER_Encoder_Read_INST_INT_IRQN);   //编码器定时器使能
    NVIC_EnableIRQ(GPIO_MULTIPLE_GPIOA_INT_IRQN);       //GPIOA中断使能
    DL_Timer_startCounter(TIMER_Encoder_Read_INST);     //开启编码器定时器计时
    NVIC_ClearPendingIRQ(UART_0_INST_INT_IRQN);         //清除UART0中断标志位
    NVIC_EnableIRQ(UART_0_INST_INT_IRQN);               //UART0中断使能
    NVIC_ClearPendingIRQ(UART_1_INST_INT_IRQN);         //清除UART1中断标志位
    NVIC_EnableIRQ(UART_1_INST_INT_IRQN);               //UART1中断使能
    // NVIC_EnableIRQ(ADC12_1_INST_INT_IRQN);              //ADC中断使能


    //OLED初始化
    printf("Starting OLED initialization...\r\n");
    OLED_Init();                 //硬件I2C-OLED初始化
    delay_ms(200);
    OLED_Clear();                //OLED清屏
    delay_ms(100);
    control_PID_Init();          //PID控制器初始化
    motor_Direction_Set(0,0);    //电机初始化
    // tracking_Init();             //循迹初始化


    /*任务系统时间初始化*/
    current_time = Get_Tick();
    uint32_t last_task01_time = current_time;
	uint32_t last_task02_time = current_time;
    

    Mode = 0;   //初始化为待机模式
    display_OLED_mode = 0;  //初始化OLED显示模式

    //OLED诊断测试
    printf("=== OLED Diagnostic Start ===\r\n");
    printf("CCS Theia Version: Compatible with SDK ********\r\n");
    printf("I2C OLED Address: 0x3C\r\n");
    printf("SDA Pin: PA10, SCL Pin: PA11\r\n");

    //检查I2C引脚状态
    Check_I2C_Pins();

    //检查CCS配置
    OLED_Check_CCS_Config();

    //扫描I2C设备
    I2C_Scan_Devices();

    //检测OLED设备
    if(!OLED_Check_Device()) {
        printf("ERROR: OLED not detected!\r\n");
        printf("Check connections:\r\n");
        printf("- VCC to 3.3V\r\n");
        printf("- GND to Ground\r\n");
        printf("- SDA to PA10\r\n");
        printf("- SCL to PA11\r\n");
    }

    //测试I2C通信
    printf("Testing I2C communication...\r\n");

    //强制显示测试
    OLED_Display_On(); //确保显示开启
    delay_ms(100);

    //清屏并测试基本显示
    OLED_Clear();
    delay_ms(100);

    //测试单个像素点
    OLED_WR_Byte(0xB0, OLED_CMD); //设置页地址0
    OLED_WR_Byte(0x00, OLED_CMD); //设置列低地址
    OLED_WR_Byte(0x10, OLED_CMD); //设置列高地址
    OLED_WR_Byte(0xFF, OLED_DATA); //写入数据

    printf("OLED basic test completed\r\n");

    //测试字符显示
    printf("Testing OLED display...\r\n");
    OLED_ShowString(0, 0, (uint8_t*)"OLED Test", 8);
    delay_ms(500);
    OLED_ShowString(0, 1, (uint8_t*)"CCS Theia", 8);
    delay_ms(500);
    OLED_ShowString(0, 2, (uint8_t*)"MSPM0G3507", 8);
    delay_ms(500);
    OLED_ShowString(0, 3, (uint8_t*)"Working!", 8);

    printf("UART0: 115200 bps\r\n");
    printf("UART1: 115200 bps (JY901S)\r\n");
    printf("=== OLED Diagnostic End ===\r\n");

    //如果到这里OLED还是不显示，尝试强制刷新
    printf("Force refresh OLED...\r\n");
    OLED_Display_On();
    delay_ms(100);

    //测试填充整个屏幕
    printf("Testing full screen fill...\r\n");
    for(int page = 0; page < 8; page++) {
        OLED_WR_Byte(0xB0 + page, OLED_CMD); //设置页地址
        OLED_WR_Byte(0x00, OLED_CMD);        //设置列低地址
        OLED_WR_Byte(0x10, OLED_CMD);        //设置列高地址
        for(int col = 0; col < 128; col++) {
            OLED_WR_Byte(0xFF, OLED_DATA);   //写入数据
        }
    }
    delay_ms(2000);

    //清屏
    OLED_Clear();
    delay_ms(1000);

    printf("OLED test completed. Check display!\r\n");
    delay_ms(2000); //显示2秒后进入正常程序
    while (1) 
    {
        control_Proc();             //主控进程
        display_Remind_Proc();      //声光提示进程
        com_UART0Receive_Handle();  //UART0接收数据包成功标志位置位以后进行数据处理


        current_time = Get_Tick();
        /*按键进程*/
        if(current_time - last_task01_time >= TASK_BUTTON_DELAY)
        {
            // TODO Task01
            last_task01_time = current_time;
            button_Proc();
        }

        /*OLED进程*/
        if(current_time - last_task02_time >= TASK_OLED_DELAY)
        {
            // TODO Task02
            last_task02_time = current_time;
            display_OLED_Proc(display_OLED_mode);
        }

    }
}


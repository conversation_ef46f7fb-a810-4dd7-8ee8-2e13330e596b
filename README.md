## Example Summary

The following example configures TimerG0 to measures the time from the start of
the capture operation to the signal edge.

## Peripherals & Pin Assignments

| Peripheral | Pin | Function |
| --- | --- | --- |
| GPIOA | PA0 | Open-Drain Output |
| GPIOA | PA15 | Standard Output |
| SYSCTL |  |  |
| TIMG6 | PB2 | Capture/Compare Pin 0 |
| EVENT |  |  |
| DEBUGSS | PA20 | Debug Clock |
| DEBUGSS | PA19 | Debug Data In Out |

## BoosterPacks, Board Resources & Jumper Settings

Visit [LP_MSPM0G3507](https://www.ti.com/tool/LP-MSPM0G3507) for LaunchPad information, including user guide and hardware files.

| Pin | Peripheral | Function | LaunchPad Pin | LaunchPad Settings |
| --- | --- | --- | --- | --- |
| PA0 | GPIOA | PA0 | J27_9 | <ul><li>PA0 is 5V tolerant open-drain so it requires pull-up<br><ul><li>`J19 1:2` Use 3.3V pull-up<br><li>`J19 2:3` Use 5V pull-up</ul><br><li>PA0 can be connected to LED1<br><ul><li>`J4 ON` Connect to LED1<br><li>`J4 OFF` Disconnect from LED1</ul></ul> |
| PA15 | GPIOA | PA15 | J3_30 | <ul><li>This pin can be used for testing purposes in boosterpack connector<ul><li>Pin can be reconfigured for general purpose as necessary</ul></ul> |
| PB2 | TIMG6 | CCP0 | J1_9 | N/A |
| PA20 | DEBUGSS | SWCLK | N/A | <ul><li>PA20 is used by SWD during debugging<br><ul><li>`J101 15:16 ON` Connect to XDS-110 SWCLK while debugging<br><li>`J101 15:16 OFF` Disconnect from XDS-110 SWCLK if using pin in application</ul></ul> |
| PA19 | DEBUGSS | SWDIO | N/A | <ul><li>PA19 is used by SWD during debugging<br><ul><li>`J101 13:14 ON` Connect to XDS-110 SWDIO while debugging<br><li>`J101 13:14 OFF` Disconnect from XDS-110 SWDIO if using pin in application</ul></ul> |

## 引脚配置说明

### OLED屏幕 (I2C接口)
- **SDA**: PA11 (I2C数据线)
- **SCL**: PA12 (I2C时钟线)
- **VCC**: 3.3V
- **GND**: 接地
- **I2C地址**: 0x3C
- **I2C速度**: Fast Mode (400kHz)

### JY901S传感器 (UART接口)
- **RX**: PB7 (MCU接收JY901S数据)
- **TX**: PB6 (MCU发送给JY901S)
- **VCC**: 3.3V/5V
- **GND**: 接地
- **波特率**: 115200 bps

### 调试串口 (UART0)
- **RX**: PA31 (接收调试数据)
- **TX**: PA28 (发送调试数据)
- **波特率**: 115200 bps

### 按键配置
- **KEY1**: PA0 (下拉输入，按下时低电平)
- **KEY2**: PA13 (下拉输入，按下时低电平)
- **KEY3**: PA14 (下拉输入，按下时低电平)

## 功能说明

### OLED显示功能
- 系统启动时显示初始化信息
- 主菜单显示各种模式选择
- JY901S数据实时显示模式

### JY901S数据输出
- 加速度数据：实时输出到串口
- 角度数据：实时输出到串口
- 温度数据：实时输出到串口
- 位置和速度数据：计算后输出

### 串口输出格式
```
JY901S_Acc: ax=0.12 ay=-0.05 az=9.78 T=25.3
JY901S_Angle: wx=0.15 wy=-0.23 wz=180.45 T=25.3
```

### 使用方法
1. 上电后OLED显示初始化信息
2. 进入主菜单，选择"JY901S"模式
3. 按KEY1打印完整JY901S数据到串口
4. 按KEY3返回主菜单
5. 串口实时输出JY901S传感器数据

### CCS Theia特定说明
- **SDK版本兼容性**：项目使用SDK ********，CCS Theia自动使用兼容版本********
- **SysConfig版本**：项目使用1.20.0，CCS Theia自动使用兼容版本1.21.1
- **编译警告**：已修复所有类型转换警告，不影响功能

### 故障排除
- **OLED不亮**：
  1. 检查硬件连接（VCC→3.3V, GND→GND, SDA→PA11, SCL→PA12）
  2. 查看串口输出的I2C扫描结果
  3. 确认"Device found at address: 0x3C"信息
  4. 检查OLED模块是否损坏
- **串口无数据**：检查波特率设置(115200)和串口连接
- **JY901S无数据**：检查UART1连接和JY901S电源
- **CCS Theia编译问题**：清理项目后重新编译，确保配置文件正确生成

### Device Migration Recommendations
This project was developed for a superset device included in the LP_MSPM0G3507 LaunchPad. Please
visit the [CCS User's Guide](https://software-dl.ti.com/msp430/esd/MSPM0-SDK/latest/docs/english/tools/ccs_ide_guide/doc_guide/doc_guide-srcs/ccs_ide_guide.html#sysconfig-project-migration)
for information about migrating to other MSPM0 devices.

### Low-Power Recommendations
TI recommends to terminate unused pins by setting the corresponding functions to
GPIO and configure the pins to output low or input with internal
pullup/pulldown resistor.

SysConfig allows developers to easily configure unused pins by selecting **Board**→**Configure Unused Pins**.

For more information about jumper configuration to achieve low-power using the
MSPM0 LaunchPad, please visit the [LP-MSPM0G3507 User's Guide](https://www.ti.com/lit/slau873).

## Example Usage
Connect to PB2 (GPIO_TIMER_CAPTURE_C0_PIN) to an edge generator source (it can
also be connected to a momentary switch).
Compile, load and run the example.
Generate a rising edge on PB2 (GPIO_TIMER_CAPTURE_C0_PIN). This will cause the
application to hit the breakpoint instruction and the value of `edgeCapture` can
be inspected to determine the time when the edge occurred.

After the variable `edgeCapture` is inspected, the user can resume
execution and detect a new rising edge.

LED_1 will toggle every time a rising edge is detected and USER_TEST_PIN GPIO
will mimic the behavior of the LED pin on the BoosterPack header and can be used
to verify the LED behavior.
